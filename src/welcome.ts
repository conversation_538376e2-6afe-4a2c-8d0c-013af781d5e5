import { ALL, Config, Controller, Get, Inject, Query } from '@midwayjs/decorator';
import { Context } from '@midwayjs/koa';
import { Param } from '@midwayjs/decorator';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { MoreThanOrEqual, Not, Repository } from 'typeorm';
import { Utils } from './comm/utils';
import axios from 'axios';
const moment = require('moment-timezone');
import * as _ from 'lodash';
import { PortalArticleEntity } from './modules/base/entity/portal/Article';
import { PortalArticleLogEntity } from './modules/base/entity/portal/ArticleLog';
import { PortalService } from './modules/base/service/common/portal';
import { CcCommonMember } from './modules/base/entity/forum/CcCommonMember';

/**
 * 欢迎界面
 */
@Controller('/')
export class WelcomeController {
  @Inject()
  ctx: Context;

  @Inject()
  utils: Utils;

  @Config('serviceUrl')
  serviceUrl;

  @InjectEntityModel(PortalArticleEntity)
  portalArticleEntity: Repository<PortalArticleEntity>;

  @InjectEntityModel(PortalArticleLogEntity)
  portalArticleLogEntity: Repository<PortalArticleLogEntity>;

  @InjectEntityModel(CcCommonMember, 'forum')
  ccCommonMember: Repository<CcCommonMember>;

  @Inject()
  portalService: PortalService;
  

  @Get('/', { summary: 'Home' })
  public async home() {
    let res = await axios.get(this.serviceUrl.showRelease);

    const events = res.data.data.events;
    
    const indexImagesLevel1 = res.data.data.index_image.level1;
    const indexImagesLevel2 = res.data.data.index_image.level2;
    const indexImagesLevel3 = res.data.data.index_image.level3;

    res = await axios.get(this.serviceUrl.wwwCDG);
    const category = '-1';

    //headerbanner
    let headerbanner = res.data.data.filter(el => el.type == 'headerbanner' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.category?.includes(category));

    if (headerbanner.length) {
      headerbanner = _.sample(headerbanner);
    }

    //float
    let float1 = res.data.data.filter(el => el.type == 'float' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 1 && el?.parameters?.extra?.category?.includes(category));

    if (float1.length) {
      float1 = _.sample(float1);
    }

    let float2 = res.data.data.filter(el => el.type == 'float' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 2 && el?.parameters?.extra?.category?.includes(category));

    if (float2.length) {
      float2 = _.sample(float2);
    }

    //footerbanner
    let footerbanner = res.data.data.filter(el => el.type == 'footerbanner' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 2 && el?.parameters?.extra?.category?.includes(category));

    if (footerbanner.length) {
      footerbanner = _.sample(footerbanner);
      footerbanner.parameters.html = footerbanner?.parameters?.html?.replace(/\s*style\s*=\s*["'][^"']*["']/gi, '');
    }

    const bannerIndex = await this.portalService.getPortalBannerIndex();

    const articles: any = await this.portalArticleEntity.find({
      where: {
        status: MoreThanOrEqual(0),
        category: Not('Events')
      },
      order: {
        displayOrder: 'DESC',
        id: 'DESC'
      },
      take: 10
    });

    // 基于displayOrder的特定位置排序
    const sortedArticles = new Array(articles.length);
    const articlesWithSpecificOrder = [];
    const articlesWithoutSpecificOrder = [];

    // 分类文章：有特定displayOrder（1-3）和其他文章
    for (const article of articles) {
      if (article.displayOrder >= 1 && article.displayOrder <= 3) {
        articlesWithSpecificOrder.push(article);
      } else {
        articlesWithoutSpecificOrder.push(article);
      }
    }

    // 先将有特定displayOrder的文章放到对应位置
    for (const article of articlesWithSpecificOrder) {
      let targetIndex;
      // 调整位置映射：3→0, 2→1, 1→2
      if (article.displayOrder === 3) {
        targetIndex = 0;
      } else if (article.displayOrder === 2) {
        targetIndex = 1;
      } else if (article.displayOrder === 1) {
        targetIndex = 2;
      } else {
        continue; // 跳过其他值
      }
      
      if (targetIndex < sortedArticles.length && !sortedArticles[targetIndex]) {
        sortedArticles[targetIndex] = article;
      }
    }

    // 用其他文章填充剩余位置
    let remainingIndex = 0;
    for (let i = 0; i < sortedArticles.length; i++) {
      if (!sortedArticles[i] && remainingIndex < articlesWithoutSpecificOrder.length) {
        sortedArticles[i] = articlesWithoutSpecificOrder[remainingIndex];
        remainingIndex++;
      }
    }

    // 更新articles数组
    articles.length = 0;
    articles.push(...sortedArticles.filter(article => article !== undefined));

    let idx = 1;
    for(let article of articles) {
      article.idx = idx;
      idx++;
    }

    await this.ctx.render('www/index', {
      events,
      articles,
      indexImagesLevel1,
      indexImagesLevel2,
      indexImagesLevel3,
      headerbanner,
      float1,
      float2,
      footerbanner,
      bannerIndex,
      category: 'Home',
      idUrl: this.serviceUrl.idUrl,
      formatDateTime: this.utils.formatDateTime,
      timestampToDate: this.utils.timestampToDate,
      newFlag: this.utils.newFlag,
    });
  }

  @Get('/article/:id', { summary: 'Article' })
  public async article(@Param('id') id: number) {
    let res = await axios.get(this.serviceUrl.showRelease);

    const events = res.data.data.events;

    res = await axios.get(this.serviceUrl.wwwCDG);
    const category = '-1';

    //headerbanner
    let headerbanner = res.data.data.filter(el => el.type == 'headerbanner' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.category?.includes(category));

    if (headerbanner.length) {
      headerbanner = _.sample(headerbanner);
    }

    const article = await this.portalArticleEntity.findOneBy({ id });

    if (!article) {
      this.ctx.status = 404;
      return;
    }

    // 增加文章访问次数
    await this.portalArticleEntity.update({ id }, { hits: article.hits + 1 });

    // 记录访问日志
    const currentTime = this.utils.now();
    let uid = 0;
    let username = '';

    // 尝试从cookie获取用户信息
    try {
      const userinfo = this.utils.decodeDZAuthkey(this.ctx);
      if (userinfo) {
        const userinfoArray = userinfo.split('\t');
        if (userinfoArray.length >= 2) {
          uid = parseInt(userinfoArray[1], 10) || 0;

          const member = await this.ccCommonMember.findOne({
            where: {
              uid,
            },
          });

          username = member.username;
        }
      }
    } catch (error) {
      // 如果获取用户信息失败，使用默认值（匿名用户）
      uid = 0;
      username = '';
    }

    // 创建访问日志记录
    const articleLog = new PortalArticleLogEntity();
    articleLog.articleId = id;
    articleLog.hitsDatetime = currentTime;
    articleLog.uid = uid;
    articleLog.username = username;
    articleLog.viewDatetime = currentTime;

    await this.portalArticleLogEntity.save(articleLog);

    // 检查content是否包含HTML标签
    const hasHtmlTags = /<[^>]+>/.test(article.content);

    await this.ctx.render('www/article', {
      events,
      article,
      headerbanner,
      category: "",
      idUrl: this.serviceUrl.idUrl,
      formatDateTime: this.utils.formatDateTime,
      timestampToDate: this.utils.timestampToDate,
      newFlag: this.utils.newFlag,
      hasHtmlTags,
    });
  }

  @Get('/category/:category/:page?', { summary: 'Article' })
  public async category(
    @Param('category') category: string,
    @Param('page') pageParam?: string
  ) {
    let page = parseInt(pageParam || '1');
    const pageSize = 7;

    // Validate page number
    if (page < 1) {
      page = 1;
    }

    let res = await axios.get(this.serviceUrl.showRelease);
    const events = res.data.data.events;

    res = await axios.get(this.serviceUrl.wwwCDG);
    const cate = '-1';

    //headerbanner
    let headerbanner = res.data.data.filter(el => el.type == 'headerbanner' &&
      el?.targets?.indexOf('portal') != -1 &&
      el?.parameters?.extra?.category?.includes(cate));

    if (headerbanner.length) {
      headerbanner = _.sample(headerbanner);
    }

    //float
    let float1 = res.data.data.filter(el => el.type == 'float' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 1 && el?.parameters?.extra?.category?.includes(cate));

    if (float1.length) {
      float1 = _.sample(float1);
    }

    let float2 = res.data.data.filter(el => el.type == 'float' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 2 && el?.parameters?.extra?.category?.includes(cate));

    if (float2.length) {
      float2 = _.sample(float2);
    }

    const [articles, total] = await this.portalArticleEntity.findAndCount({
      where: {
        category,
        status: MoreThanOrEqual(0)
      },
      order: {
        displayOrder: 'DESC',
        id: 'DESC'
      },
      skip: (page - 1) * pageSize,
      take: pageSize
    });

    let idx = 1;
    for(let article of articles) {
      //@ts-ignore
      article.idx = idx;
      idx++;
    }

    const totalPages = Math.ceil(total / pageSize);

    await this.ctx.render('www/category', {
      events,
      articles,
      headerbanner,
      float1,
      float2,
      category,
      pagination: {
        current: page,
        total: totalPages,
        pageSize,
        totalItems: total
      },
      idUrl: this.serviceUrl.idUrl,
      formatDateTime: this.utils.formatDateTime,
      timestampToDate: this.utils.timestampToDate,
      newFlag: this.utils.newFlag,
    });
  }

  @Get('/search', { summary: 'Search' })
  public async search(@Query(ALL) params: any) {
    let res = await axios.get(this.serviceUrl.showRelease);
    const events = res.data.data.events;

    res = await axios.get(this.serviceUrl.wwwCDG);
    const cate = '-1';

    //headerbanner
    let headerbanner = res.data.data.filter(el => el.type == 'headerbanner' &&
      el?.targets?.indexOf('portal') != -1 &&
      el?.parameters?.extra?.category?.includes(cate));

    if (headerbanner.length) {
      headerbanner = _.sample(headerbanner);
    }

    //float
    let float1 = res.data.data.filter(el => el.type == 'float' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 1 && el?.parameters?.extra?.category?.includes(cate));

    if (float1.length) {
      float1 = _.sample(float1);
    }

    let float2 = res.data.data.filter(el => el.type == 'float' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 2 && el?.parameters?.extra?.category?.includes(cate));

    if (float2.length) {
      float2 = _.sample(float2);
    }

    if (!params.q?.trim()) {
      await this.ctx.render('www/search', {
        events,
        articles: [],
        headerbanner,
        float1,
        float2,
        category: "",
        q: '',
        pagination: {
          current: 1,
          total: 0,
          totalItems: 0
        },
        formatDateTime: this.utils.formatDateTime,
        timestampToDate: this.utils.timestampToDate,
        newFlag: this.utils.newFlag,
      });
      return;
    }

    let page = parseInt(params.page || '1');
    if (page < 1) {
      page = 1;
    }

    const {list, pagination} = await this.portalService.search(params);

    let idx = 1;
    for(let article of list) {
      article.idx = idx;
      idx++;
    }

    const totalPages = Math.ceil(pagination.total / pagination.size);

    await this.ctx.render('www/search', {
      events,
      articles: list,
      headerbanner,
      float1,
      float2,
      category: "",
      q: params.q,
      pagination: {
        current: page,
        total: totalPages,
        totalItems: pagination.total
      },
      formatDateTime: this.utils.formatDateTime,
      timestampToDate: this.utils.timestampToDate,
      newFlag: this.utils.newFlag,
    });
  }

  @Get(/^\/[Pp][Rr][Ii][Vv][Aa][Cc][Yy]\.html$/i, { summary: 'Privacy Policy' })
  public async privacy() {
    await this.ctx.render('www/Privacy');
  }

  @Get(/^\/[Pp][Rr][Ii][Vv][Aa][Cc][Yy]-[Hh][Ww]\.html$/i, { summary: 'Privacy Policy HW' })
  public async privacyHW() {
    await this.ctx.render('www/Privacy-HW');
  }
}
