<head>
    <meta charset="utf-8" />
    <title>
      ChaseDream - MBA申请、商学院Master/PhD申请，GMAT备考（逐梦网）
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="referrer" content="no-referrer-when-downgrade" />
    <meta name="description"
      content="ChaseDream是国际商学院申请与职业发展交流平台。包括：MBA申请，商科Master/PhD申请，GMAT/TOEFL考试，商学院学习与生活，职业发展等。ChaseDream成立于2003年，网友们亲切地称之为CD。" />
    <meta name="keywords" content="MBA,MBA申请,Master,Master申请,PhD,PhD申请,GMAT,GMAT考试" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <link rel="icon" href="/public/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/public/css/search.css" type="text/css" />
    <link rel="stylesheet" href="/public/css/login-modal.css" type="text/css" />
    
    <style>
      html {
        background-color: #ECF6FC;
      }
      
      body {
        margin: 0 auto;
        padding: 10px 0 0 0;
        font-family: Tahoma, Geneva, sans-serif;
        background-color: white; 
        width: 1240px;
        font-size: 14px;
      }
      
      a {
        color: #333;
        text-decoration: none;
      }
  
      a:link,
      a:visited {
        text-decoration: none
      }
  
      a:hover {
        text-decoration: none
      }
  
      .header {
        display: flex;
        justify-content: space-between;        
        padding: 0 40px 10px 40px;        
      }
  
      .logo {
        display: flex;
        align-items: flex-end;
      }


  
      .top-banner img {
        width: 720px;
        height: 90px;
      }
  
      .nav {       
        width: 1210px;         
        height: 60px;
        font-size: 16px;
        margin-left: 15px;
        margin-right: 0px;
        margin-bottom: 20px;
        padding-right: 0px;
      }
      .event-title {
        margin: 0 0 10px 0;
        font-size: 17px;
        font-weight: 500;
        border-bottom: 1px solid #eee;
        padding-bottom: 15px;
      }
      .nav-left {
        display: flex;        
        padding-top: 28px;
        margin-left: -23px;
      }
  
      .nav-left a {
        margin: 0 25px;
        text-decoration: none;
        color: #504f4f;
        padding-bottom: 10px;       
        font-weight: bold; 
      }
  
      .nav-left a.active {
        color: #3397E9;
        border-bottom: 2px solid #3397E9;
      }
  
      .user-actions {
        display: flex;      
        margin-top: 18px;        
      }
  
      .user-actions img {
        margin-right: 15px;
      }
  
      .user-actions a {
        margin-left: 15px;
        text-decoration: none;
        color: #333;
      }
  
      .user-actions a:not(:last-child) {
        border-right: 1px solid #ddd;
        padding-right: 15px;
      }
  
      .main-content {
        display: flex;
        padding-left: 40px;
        padding-right: 40px;
      }
  
      .calendar {
        width: 260px;
        font-size: 12px;        
      }
  
      .calendar-item {
        margin-bottom: 10px;
      }
  
      .date {
        color: #999;
      }
  
      .status {
        color: #0066cc;
        margin-right: 5px;
      }
  
      .content-area {
        margin: 0 0 0 20px;    
        width: 880px;    
      }
  
      .main-banner {
        display: flex;
        width: 920px;
        height: 300px;
        gap: 20px;
      }
  
      .main-banner img {
        width: 600px;
        height: 300px;
      }
  
      .secondary-banner {
        margin: 15px 0;
        position: relative;
        z-index: 100;
      }

      /* 悬停显示的大图容器样式 */
      .event-hover-image {
        position: fixed !important;
        z-index: 99999 !important;
        display: none;
        background: white;
        border: 2px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 10px;
      }

      /* 更具体的选择器确保z-index优先级 */
      #event-hover-image.event-hover-image {
        z-index: 99999 !important;
        position: fixed !important;
      }

      /* 左侧箭头（当弹出框在右侧时） */
      .event-hover-image.show-right::before {
        content: '';
        position: absolute;
        left: -10px;
        top: var(--arrow-top, 30px);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 10px 10px 10px 0;
        border-color: transparent #ddd transparent transparent;
      }

      .event-hover-image.show-right::after {
        content: '';
        position: absolute;
        left: -8px;
        top: var(--arrow-top, 30px);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 10px 10px 10px 0;
        border-color: transparent white transparent transparent;
      }

      /* 右侧箭头（当弹出框在左侧时） */
      .event-hover-image.show-left::before {
        content: '';
        position: absolute;
        right: -10px;
        top: var(--arrow-top, 30px);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 10px 0 10px 10px;
        border-color: transparent transparent transparent #ddd;
      }

      .event-hover-image.show-left::after {
        content: '';
        position: absolute;
        right: -8px;
        top: var(--arrow-top, 30px);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 10px 0 10px 10px;
        border-color: transparent transparent transparent white;
      }

      .event-hover-image img {
        display: block;
        border-radius: 4px;
      }

      /* 当secondary-banner没有内容时，调整main-banner和news-content之间的间距为20px */
      .secondary-banner:empty {
        margin: 20px 0;
      }
  
      .secondary-banner img {
        width: 880px;
        height: 100px;      
      }
  
      .news-conetent {
        display: flex;
        justify-content: flex-start;
      }

      .news-left {
        width: 100%;
      }

      .news-item {
        display: flex;
        margin-bottom: 20px;
        align-items: flex-start;
        width: 100%;
      }

      .news-item a {
        display: flex;
        align-items: flex-start;
        text-decoration: none;
        color: inherit;
        cursor: pointer;
        flex: 1;
        margin-right: 20px;
      }

      .news-image {
        width: 200px;
        height: 140px;
        margin-right: 10px;
        object-fit: cover;
        flex-shrink: 0;
      }

      .news-content {
        flex: 1;
      }

      .news-ad {
        flex-shrink: 0;
      }

      .news-ad img {
        width: 260px;
        height: 140px;
      }
  
      .news-subtitle {
        font-size: 14px;
        color: #999;
        flex: 1;
        display: -webkit-box;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
        word-wrap: break-word;
        word-break: break-word;
      }

      .news-content {
        position: relative;
        width: 380px;
        display: flex;
        flex-direction: column;
        height: 140px;
        margin-right: 10px;
      }

      .news-content h3 {
        margin: 0 0 5px 0;
        font-size: 16px;
        width: 380px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .news-date {
        color: #999;
        font-size: 12px;
        margin-top: auto;
        align-self: flex-start;
      }
  
      .right-sidebar {
        width: 260px;
      }
  
      .sidebar {
        width: 260px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }

      .sidebar .float1{
        margin-bottom: 20px;
        width: 260px;
      }

      .sidebar .float2{
        width: 260px;
      }

      .sidebar img {
        width: 260px;
        height: 140px;
      }
  
      .footer {        
        background-color: #2f3542;
        color: #fff;
        padding: 20px;
      }
  
      .footer-links {
        width: 1240px;
        margin: 0 auto;
        text-align: center;
        font-size: 12px;
      }
  
      .footer-links a:not(:last-child) {
        border-right: 1px solid #ddd;
        padding-right: 15px;
      }
  
      .footer-links a {
        color: #fff;
        margin: 0 7px 0 7px;
        text-decoration: none;
      }
  
      .footer-text {
        width: 1240px;
        color: #999;
        margin: 0 auto;
        text-align: center;
        padding-top: 30px;
        font-size: 10px;
      }
  
      .footer-text a {
        color: #999;
      }
  
      .news-left {
        width: 640px;
        margin-right: 20px;
      }
  
      .carousel-wrapper {
        position: relative;
        width: 600px;
        height: 300px;        
        overflow: hidden;
      }

      .carousel-slides {
        position: relative;
        width: 100%;
        height: 100%;
      }

      .carousel-slides a {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: none;
      }

      .carousel-slides a:first-child {
        display: block;
      }

      .carousel-slides img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .carousel-indicators {
        position: absolute;
        bottom: 10px;
        right: 10px;
        display: flex;
        flex-direction: column;
        gap: 3px;
      }

      .indicator-row {
        display: flex;
        gap: 5px;
        justify-content: flex-end;
      }

      .indicator {
        width: 15px;
        height: 15px;
        background: #0066cc;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
      }

      .indicator.active {
        background: #003366;
      }
  
      .hevent {
        margin-bottom: 8px;
        _margin-bottom: 0px;
        _width: 100%;
      }
  
      .hevent .tm {
        height: 20px;
        padding-top: 6px;
        *height: 19px;
        *padding-top: 7px;
        overflow: hidden;
        background-image: url(../../chasedream/LframeTm.gif);
        background-repeat: no-repeat;
        background-position: top-right;
      }
  
      .hevent .tl {
        height: 26px;
        width: 4px;
        _margin-right: -3px;
        background-image: url(../../chasedream/LframeTl.gif);
        background-repeat: no-repeat;
      }
  
      .hevent .tr {
        height: 26px;
        width: 4px;
        _margin-left: -3px;
        background-image: url(../../chasedream/LframeTr.gif);
        background-repeat: no-repeat;
      }
  
      .hevent span.tt {
        font-size: 13px;
        color: #ffffff;
        font-weight: bold;
        padding-left: 5px;
      }
  
      .hevent .mm {
        padding: 5px 5px;
        overflow: hidden;
        border: 1px solid #7FBBE0;
        border-top: 0;
        border-bottom: 0;
      }
  
      .hevent .ml {
        display: none
      }
  
      .hevent .mr {
        display: none
      }
  
      .hevent .bm {
        height: 4px;
        _margin-top: -11px;
        border-bottom: 1px solid #7FBBE0;
      }
  
      .hevent .bl {
        height: 5px;
        width: 5px;
        _margin-right: -3px;
        background-image: url(../../chasedream/LframeBl.gif);
        background-repeat: no-repeat;
      }
  
      .hevent .br {
        height: 5px;
        width: 5px;
        _margin-left: -3px;
        background-image: url(../../chasedream/LframeBr.gif);
        background-repeat: no-repeat;
      }
  
      .hevent,
      .hevent a {
        font-size: 14px;
        color: #333333;
      }
  
      .hevent td {
        padding: 0 0 0 1px;
        vertical-align: top;
        line-height: 20px;
      }
  
      .hevent ul {
        padding: 0;
        margin: 0;
        list-style-type: none;
      }
  
      .hevent .bg {
        background-color: #EBF6FE;
      }
  
      .hevent .mm {
        padding: 5px 0px;
      }
  
      .slip {
        display: block
      }
  
      .slipnone {
        display: none
      }
  
      .listTable {
        /* border-collapse: separate;
        border-spacing: 0 5px; */
      }
  
      .listTable tr td:nth-of-type(1) {
        text-align: center;
        color: #777;
        font-size: 13px;
        position: relative;
      }
  
      .listTable tr td:nth-of-type(1) i {
        background: url(https://www.chasedream.com/images/icon_newest.png) no-repeat 0 0;
        width: 6px;
        height: 6px;
        position: absolute;
        left: 0px;
        top: 0px;
      }
  
      .listTable tr td:nth-of-type(1) span {
        color: #0b7cc3;
        font-size: 14px;
      }
  
      .listTable tr td:nth-of-type(2) {
        padding-left: 12px;
        position: relative;
        font-size: 12px;
        line-height: 20px;
        color: #333;
      }
  
      .listTable tr td:nth-of-type(2) span {
        position: absolute;
        width: 8px;
        height: 8px;
        left: 0;
        top: 5px;
        background: url(https://mat.chasedream.com/chasedream/www/icon_new.png) no-repeat;
        z-index: 10;
      }
  
      .listTable tr td:nth-of-type(2) i {
        position: absolute;
        width: 1px;
        height: 100%;
        background: #d9d9d9;
        left: 4px;
      }
  
      .listTable tr:first-child td:nth-of-type(2) i {
        top: 10px;
      }
  
      .listTable tr:last-child td:nth-of-type(2) i {
        height: 5px;
      }
  
  
      /*给广告图片加上广告小图*/
      a.cdg-content {
        position: relative;
        display: inline-block;
      }
  
      a.cdg-content img {
        vertical-align: top;
      }
  
      a.cdg-content span {
        opacity: 0.5;
        width: 24px;
        height: 14px;
        font-size: 10px;
        display: block;
        text-align: center;
        line-height: 14px;
      }
  
      .w1 span,
      .w2 span,
      .w3 span,
      .w4 span {
        background: #fff;
        color: #aaa;
        position: absolute;
      }
  
      .w1 span {
        left: 0;
        top: 0;
      }
  
      .w2 span {
        right: 0;
        top: 0;
      }
  
      .w3 span {
        left: 0;
        bottom: 0;
      }
  
      .w4 span {
        right: 0;
        bottom: 0;
      }
  
      .b1 span,
      .b2 span,
      .b3 span,
      .b4 span {
        background: #000;
        color: #fff;
        position: absolute;
      }
  
      .b1 span {
        left: 0;
        top: 0;
      }
  
      .b2 span {
        right: 0;
        top: 0;
      }
  
      .b3 span {
        left: 0;
        bottom: 0;
      }
  
      .b4 span {
        right: 0;
        bottom: 0;
      }
  
      .mframe .wrapper .mm .sResult .sum {
        height: 0;
      }
  
      .wwwCDGWrap {
        margin-bottom: 10px
      }

      .nav-content {
        width: 1160px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;        
        border-bottom: 1px solid #eee;
      }
    </style>
  </head>

  <%- include('./header-nav') %>
