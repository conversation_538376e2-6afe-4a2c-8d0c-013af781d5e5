<html>
<%- include('./partials/header-article') %>
<head>
  <link rel="stylesheet" href="/public/css/sticky.css" type="text/css" />
</head>
<body>
  <div class="main-content">
    <div class="content-area">
      <div class="content-area-title"><%= article.title %></div>
      <div class="content-area-desc">
        <span class="desc-l1">作者：<%= article.author %></span>
        <span class="desc-r1">日期：<%= timestampToDate(article.datetime, 'YYYY-MM-DD') %></span>
      </div>
      <% if (hasHtmlTags) { %>
        <div class="content"><%- article.content %></div>
      <% } else { %>
        <div id="vditor-preview" class="content"></div>
      <% } %>
    </div>
    <div class="sticky-container" id="article-events-container">
      <div class="sticky-element" id="article-events-sticky">
        <%- include('./partials/events') %>
      </div>
      <div class="sticky-placeholder" id="article-events-placeholder"></div>
    </div>
  </div>

  <!-- 悬停显示的大图容器 - 移到最高层级避免层叠上下文问题 -->
  <div id="event-hover-image" class="event-hover-image">
    <a href="" target="_blank">
      <img src="" width="600" height="300" alt="活动图片" />
    </a>
  </div>

  <!-- 登录弹出框 -->
  <div class="login-modal-overlay" id="login-modal-overlay">
    <div class="login-modal">
      <svg class="login-modal-close" id="login-modal-close" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="width: 24px; height: 24px;"><g fill="none"><path d="M4.5 4.5L19.5 19.5" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M19.5 4.5L4.5 19.5" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>      
      <iframe class="login-modal-iframe" src="<%= idUrl%>/login-www-pc?t=<%= Math.random() %>" frameborder="0"></iframe>
    </div>
  </div>
  
  <!-- 引入公共JavaScript文件 -->
  <script src="/public/js/common.js"></script>
  <script src="/public/js/login-modal.js"></script>
  <script src="/public/js/sticky.js"></script>
  
  <% if (!hasHtmlTags) { %>
  <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', () => {
      // 获取Markdown内容
      const content = `<%- article.content %>`;

      // 初始化Vditor预览
      Vditor.preview(document.getElementById('vditor-preview'), content, {
        mode: 'dark',
        theme: {
          current: 'light'
        },
        markdown: {
          toc: true,
          math: true,
          mermaid: true
        },
        after: () => {
          // 渲染完成后的回调
          console.log('Markdown渲染完成');
        }
      });
    });
  </script>
  <% } else { %>
  <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', () => {
      console.log('HTML内容直接展示，无需Markdown渲染');
    });
  </script>
  <% } %>

  <%- include('./partials/footer') %>
</body>
</html>
