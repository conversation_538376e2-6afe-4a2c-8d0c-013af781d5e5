/**
 * 文本省略号处理脚本
 * 为使用.news-subtitle类的元素添加多行文本截断和省略号功能
 */

function addTextEllipsis() {
  // 查找所有需要处理的元素
  const subtitles = document.querySelectorAll('.news-subtitle');
  
  subtitles.forEach(subtitle => {
    // 保存原始文本
    if (!subtitle.dataset.originalText) {
      subtitle.dataset.originalText = subtitle.textContent;
    }
    
    const originalText = subtitle.dataset.originalText;
    
    // 检查是否需要截断
    subtitle.textContent = originalText;
    
    if (subtitle.scrollHeight > subtitle.clientHeight) {
      // 清理文本：移除换行符和多余空格
      let cleanText = originalText.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
      
      // 使用二分查找找到合适的文本长度
      let left = 0;
      let right = cleanText.length;
      let bestLength = 0;
      
      while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        const testText = cleanText.substring(0, mid) + '...';
        
        // 临时设置文本来测试高度
        subtitle.textContent = testText;
        
        if (subtitle.scrollHeight <= subtitle.clientHeight) {
          bestLength = mid;
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }
      
      // 设置最终的截断文本
      const finalText = cleanText.substring(0, bestLength) + '...';
      subtitle.textContent = finalText;
    }
  });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  addTextEllipsis();
});

// 窗口大小改变时重新计算
window.addEventListener('resize', function() {
  // 防抖处理
  clearTimeout(window.ellipsisResizeTimer);
  window.ellipsisResizeTimer = setTimeout(addTextEllipsis, 250);
});

// 导出函数供其他脚本使用
if (typeof window !== 'undefined') {
  window.addTextEllipsis = addTextEllipsis;
}
