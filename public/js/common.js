/**
 * 通用JavaScript函数
 */

// 轮播图初始化和控制
function initCarousel() {
  const slideLinks = document.querySelectorAll('.carousel-slides a');
  const indicators = document.querySelectorAll('.carousel-indicators .indicator');

  if (!slideLinks.length || !indicators.length) return;

  // 防止重复初始化
  if (window.carouselInitialized) return;
  window.carouselInitialized = true;

  let currentSlide = 0;

  // 为每个指示器添加点击事件，使用data-slide-index直接获取索引
  indicators.forEach((indicator) => {
    indicator.onclick = () => {
      const slideIndex = parseInt(indicator.getAttribute('data-slide-index'));
      goToSlide(slideIndex);
    };
  });

  function goToSlide(index) {
    // 隐藏所有幻灯片
    slideLinks.forEach((slide, i) => {
      slide.style.display = i === index ? 'block' : 'none';
    });

    // 更新指示器状态
    indicators.forEach((ind) => {
      const slideIndex = parseInt(ind.getAttribute('data-slide-index'));
      // 保持原有的类名，只切换active状态
      const baseClasses = ind.className.replace(/\s*active\s*/, '').trim();
      ind.className = `${baseClasses} ${slideIndex === index ? 'active' : ''}`.trim();
    });

    currentSlide = index;
  }

  // 自动轮播 - 保存定时器ID以便后续管理
  window.carouselTimer = setInterval(() => {
    currentSlide = (currentSlide + 1) % slideLinks.length;
    goToSlide(currentSlide);
  }, 3000);

  // 初始化时激活第一个指示器
  if (indicators.length > 0) {
    indicators[0].className = `${indicators[0].className} active`.trim();
  }

  goToSlide(0);
}

// 表格行悬停效果
function initTableHover() {
  const tableRows = document.querySelectorAll('.listTable tr');

  tableRows.forEach(row => {
    row.addEventListener('mouseenter', () => {
      row.classList.add('bg');
    });

    row.addEventListener('mouseleave', () => {
      row.classList.remove('bg');
    });
  });
}

// 元素轮换显示
function setupRotation(selector) {
  const elements = document.querySelectorAll(selector + ' a');

  // 只在找到元素时继续
  if (elements.length === 0) {
    return null; // 如果没有找到元素则返回null
  }

  // 如果只有一个元素，不需要轮换
  if (elements.length === 1) {
    elements[0].style.display = 'block';
    return null;
  }

  let currentIndex = 0;

  // 初始化：隐藏所有元素，然后显示第一个
  elements.forEach((element, index) => {
    element.style.display = index === 0 ? 'block' : 'none';
  });

  function rotateNext() {
    // 隐藏当前元素
    if (elements[currentIndex]) {
      elements[currentIndex].style.display = 'none';
    }

    // 移动到下一个元素
    currentIndex = (currentIndex + 1) % elements.length;

    // 显示下一个元素
    if (elements[currentIndex]) {
      elements[currentIndex].style.display = 'block';
    }
  }

  // 返回定时器ID，用于后续管理
  return setInterval(rotateNext, 2500);
}

// 初始化轮换显示
function initRotations() {
  // 只在元素存在时调用setupRotation
  const float1Elements = document.querySelector('.float1');
  const float2Elements = document.querySelector('.float2');
  const secondaryBanner = document.querySelector('.secondary-banner');

  if (float1Elements) {
    const float1Links = document.querySelectorAll('.float1 a');
    console.log('Float1 元素数量:', float1Links.length);
    setupRotation('.float1');
  }

  if (float2Elements) {
    const float2Links = document.querySelectorAll('.float2 a');
    console.log('Float2 元素数量:', float2Links.length);
    setupRotation('.float2');
  }

  if (secondaryBanner) {
    const bannerLinks = document.querySelectorAll('.secondary-banner a');
    console.log('Secondary banner 元素数量:', bannerLinks.length);
    setupRotation('.secondary-banner');
  }
}

// JSONP回调函数
function jsonpCallback(data) {
  var ads = getByClass('cd-ads');
  if (!data) {
    for (var i = 0; i < ads.length; i++) {
      removeClass(ads[i], 'cd-ads');
    }
  }
}

// 获取JSONP数据
function get_jsonp() {
  var JSONP = document.createElement("script");
  JSONP.id = "jsonp_script";
  JSONP.type = "text/javascript";
  JSONP.src = "https://tool.chasedream.com/iptools/ip/ise?callback=jsonpCallback";
  document.getElementsByTagName("head")[0].appendChild(JSONP);
}

// 通过类名获取元素
function getByClass(sClass, parent) {
  var aEles = (parent || document).getElementsByTagName('*');
  var arr = [];
  for (var i = 0; i < aEles.length; i++) {
    // 确保 className 存在且是字符串类型
    if (aEles[i].className && typeof aEles[i].className === 'string') {
      var aClass = aEles[i].className.split(' ');
      for (var j = 0; j < aClass.length; j++) {
        if (aClass[j] == sClass) {
          arr.push(aEles[i]);
          break;
        }
      }
    }
  }
  return arr;
}

// 移除类名
function removeClass(obj, sClass) {
  var aClass = obj.className.split(' ');
  if (!aClass[0]) return;
  for (var i = 0; i < aClass.length; i++) {
    if (aClass[i] == sClass) {
      aClass.splice(i, 1);
      obj.className = aClass.join(' ');
      return;
    }
  }
}

// 广告处理
var addCss = {
  init: function () {
    if (this.getClass('cdg-content').length < 1) {
      return false;
    }
    this.creatAdTx(this.getClass('w1'));
    this.creatAdTx(this.getClass('w2'));
    this.creatAdTx(this.getClass('w3'));
    this.creatAdTx(this.getClass('w4'));
    this.creatAdTx(this.getClass('b1'));
    this.creatAdTx(this.getClass('b2'));
    this.creatAdTx(this.getClass('b3'));
    this.creatAdTx(this.getClass('b4'));
  },
  creatAdTx: function (ads) {
    var adText = '<span>广告</span>'
    if (ads.length < 1) {
      return false;
    }
    for (var i = 0; i < ads.length; i++) {
      ads[i].innerHTML = ads[i].innerHTML + adText;
    }
  },
  getClass: function getByClass(sClass, parent) {
    var aEles = (parent || document).getElementsByTagName('*');
    var arr = [];
    for (var i = 0; i < aEles.length; i++) {
      // 确保 className 存在且是字符串类型
      if (aEles[i].className && typeof aEles[i].className === 'string') {
        var aClass = aEles[i].className.split(' ');
        for (var j = 0; j < aClass.length; j++) {
          if (aClass[j] == sClass) {
            arr.push(aEles[i]);
            break;
          }
        }
      }
    }
    return arr;
  }
}

// 页面初始化
function init() {
  initCarousel();
  initTableHover();
  initRotations();
  get_jsonp();
  
  // 页面加载完成后初始化广告
  window.addEventListener('load', () => {
    addCss.init();
  });
}

// 全局错误处理 - 防止动态插入的脚本出错
window.addEventListener('error', function(event) {
  // 如果是来自动态插入脚本的错误，尝试修复
  if (event.message && event.message.includes('Cannot read properties of undefined')) {
    console.warn('捕获到脚本错误，已忽略:', event.message);
    event.preventDefault();
    return true;
  }
});

// 修复页面中可能存在的错误脚本
function fixDynamicScripts() {
  // 注意：不再清除所有定时器，以免影响轮播功能
  // 如果需要清除特定的错误定时器，应该更精确地定位和清除

  // 这个函数现在主要用于处理其他动态脚本错误
  // float2 的轮换逻辑已经在 setupRotation 函数中统一处理
  console.log('动态脚本检查完成');
}

// 文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('页面DOM加载完成，开始初始化...');
  init();

  // 延迟执行脚本修复，确保动态内容已加载
  setTimeout(() => {
    fixDynamicScripts();

    // 再次检查 float2 元素状态
    const float2Links = document.querySelectorAll('.float2 a');
    console.log('初始化后 Float2 链接状态:');
    float2Links.forEach((link, index) => {
      console.log(`链接 ${index}: display = ${link.style.display || 'default'}`);
    });
  }, 100);
});
